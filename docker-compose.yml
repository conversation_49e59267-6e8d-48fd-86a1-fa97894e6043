services:
  db:
    image: mysql:8.0
    container_name: trading-app-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: trading_app_dev
      MYSQL_USER: tradinguser
      MYSQL_PASSWORD: tradingpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-prootpassword"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

volumes:
  mysql-data:
