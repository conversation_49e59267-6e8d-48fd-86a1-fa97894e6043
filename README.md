# Stock Screening Tool for Japanese Listed Companies

## Overview

This tool enables users to screen information about Japanese listed stocks.  
Users can log in to the Dashboard using accounts created via the Admin Dashboard.  
The expected number of users is around 100, primarily members of an online stock investment community.

## Features

The project consists of two main components: the **Admin Dashboard** and the **User Dashboard**.  
Features marked as `[WIP]` are currently under consideration and may change.

### Admin Dashboard

- Admin login
- User management
- Admin account creation and management
- Inquiry management (view inquiries from the user dashboard)
- [WIP] Stock data retrieval (from external sources)

### User Dashboard

- User login
- Inquiry submission
- [WIP] Stock screening feature for Japanese listed companies
- [WIP] CSV export functionality

## Development Roadmap

Currently in progress. This roadmap is shared with the client and includes both Japanese and English.

[Development Roadmap (Google Sheets)](https://docs.google.com/spreadsheets/d/1Vleq8HBkq8aSJ7viz3NWu4NjJRY9MFbIau3jWnFaFKc/edit?gid=0#gid=0)

## Tech Stack

- **TypeScript**
- **Next.js**
- **Prisma** – Type-safe ORM for working with MySQL
- **MySQL** – Database for storing user and stock data
- **Docker** – For creating a reproducible local development environment
- **Amazon Lightsail** – VPS used for production deployment

## System Architecture (Local / Production)

### Local Environment

- MySQL (via Docker)
- Next.js (`npm run dev`)
- Prisma (for schema and ORM)

### Production Environment

- AWS Lightsail (planned)

## Version Management

- Node.js: v22.10.0
- npm: 11.4.0
- MySQL: 8.0
- Prisma: 5.10.2
- Next.js: 15.3.3
- TypeScript: 5.8.3
- React: 19.0.0

## Directory Structure

```
trading-app-server/
├── .next/                             # Next.js build output (generated)
├── node_modules/                      # Dependencies
├── messages/                          # Internationalization message files
│   ├── en.json                        # English translations
│   └── jp.json                        # Japanese translations
├── prisma/                            # Prisma ORM files
│   ├── migrations/                    # Database migrations
│   ├── schema.prisma                  # Database schema
│   └── seed.ts                        # Seed script
├── public/                            # Static assets
├── src/                               # Source code
│   ├── __tests__/                     # Test files
│   ├── app/                           # Next.js App Router
│   │   ├── [locale]/                  # Internationalized routes
│   │   │   ├── admin/                 # Admin dashboard routes
│   │   │   │   ├── _components/       # Admin-specific components
│   │   ├── api/                       # API routes
│   │   │   └── auth/                  # Authentication API
│   │   │       └── [...nextauth]/     # NextAuth.js configuration
│   ├── components/                    # Shared components
│   │   ├── ui/                        # UI components
│   ├── i18n/                          # Internationalization setup
│   ├── lib/                           # Utility libraries
│   ├── types/                         # TypeScript type definitions
│   └── middleware.ts                  # Next.js middleware
├── .env                               # Environment variables (not committed)
├── .gitignore                         # Git ignore file
├── babel.config.js                    # Babel configuration
├── components.json                    # shadcn/ui components configuration
├── docker-compose.yml                 # Docker configuration
├── Dockerfile                         # Docker image configuration
├── eslint.config.mjs                  # ESLint configuration
├── jest.config.ts                     # Jest testing configuration
├── jest.setup.ts                      # Jest setup file
├── Makefile                           # Development commands
├── next-env.d.ts                      # Next.js type definitions
├── next.config.ts                     # Next.js configuration
├── package.json                       # Project metadata and dependencies
├── package-lock.json                  # Dependency lock file
├── postcss.config.mjs                 # PostCSS configuration
├── README.md                          # Project documentation
└── tsconfig.json                      # TypeScript configuration
```

## Local Development Setup

1. **Clone the repository**

   ```bash
   https:  git clone https://github.com/gaogao-asia/trading-app-server.git
   ssh:   <NAME_EMAIL>:gaogao-asia/trading-app-server.git
   cd trading-app-server
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the MySQL database with Docker**

   ```bash
   make up
   ```

5. **Initialize the database**

   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run migrations
   npx prisma migrate dev

   # Seed the database with initial admin account
   npx prisma db seed
   ```

6. **Start the development server**

   ```bash
   npm run dev
   ```

7. **Run test case**

   ```bash
   npm run test
   ```

## Production Deployment & Operations

**📝 TODO (To be written by Sasaki):**  
This section will be written later by sasaki

## Branch & Development Rules

- **Primary flow**

  1. Create a new branch from `develop`.
  2. Implement the feature or fix.
  3. Open a Pull Request (PR) back to `develop`.

- **Pull Request requirements**

  - Include a reference to the corresponding Issue with an automatic close keyword, e.g. `Closes #42`.
  - If the PR involves UI changes, attach screenshots or a short video so reviewers can see the result.

- **Branch naming convention**
  we can immediately see who owns the branch.

<author>/<feature-name>
Examples:

- `lan/create_admin_login`
- `sasaki/user-dashboard-fixes`

## Notes for Future Enhancements

- Authentication will likely use **NextAuth.js**, but feel free to use a library you're familiar with from previous projects.
- We plan to use the **J-Quants API** for fetching stock data: [J-Quants API Documentation](https://jpx.gitbook.io/j-quants-ja/api-reference)
- API integration will come later; mock data will be used during initial development.  
  Model definitions for mock data will be provided by the project owner.
- There is no design mockup at this stage. Implement the UI/UX based on your own judgment and aim for simplicity.  
  Once the core features are in place, the client will provide feedback, and we'll iterate on UI/UX from there.
