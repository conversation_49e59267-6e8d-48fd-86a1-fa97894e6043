{"name": "trading-app-server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "prisma db seed", "test": "jest"}, "dependencies": {"@iconify/react": "^6.0.0", "@prisma/client": "^5.10.2", "@radix-ui/react-slot": "^1.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.3.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0", "prisma": "^5.10.2", "tailwindcss": "^4", "ts-jest": "^29.3.4", "ts-node": "^10.9.1", "tw-animate-css": "^1.3.4", "typescript": "^5", "wait-on": "^7.0.1"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}