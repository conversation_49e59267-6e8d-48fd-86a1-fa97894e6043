export const useTranslations = (namespace?: string) => {
  return (key: string) => {
    // Return mock translations based on key
    const mockTranslations: Record<string, string> = {
      // AdminLogin namespace
      "submit": "Login",
      "signingIn": "Signing in...",
      "title": "Admin Login",
      "email": "Email",
      "password": "Password",
      
      // AdminDashboard namespace
      "logout": "Logout",
      "signingOut": "Signing out...",
      "welcome": "Welcome! You are logged in as an admin.",
    };
    
    return mockTranslations[key] || key;
  };
};

export const useFormatter = () => ({
  dateTime: (date: Date) => date.toISOString(),
  number: (num: number) => num.toString(),
});

export const NextIntlClientProvider = ({ children }: { children: React.ReactNode }) => children;

export default {
  useTranslations,
  useFormatter,
  NextIntlClientProvider,
};
