// app/api/auth/[...nextauth]/route.ts
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcryptjs";
import { getAdminByEmail } from "@/lib/AdminService";

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required.");
        }

        const admin = await getAdminByEmail(credentials.email);

        if (!admin) {
          throw new Error("Email is not registered.");
        }

        const isValid = await compare(credentials.password, admin.password);

        if (!isValid) {
          throw new Error("Incorrect password.");
        }

        return {
          id: admin.id,
          email: admin.email,
        };
      },
    }),
  ],
  pages: {
    signIn: "/admin/login",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.user = {
          id: user.id,
          email: user.email,
        };
      }
      return token;
    },
    async session({ session, token }) {
      if (token.user) {
        session.user = token.user;
      }
      return session;
    },
  },
  events: {
    async signOut() {
      // Clear any stale tokens on sign out
    },
  },
});

export { handler as GET, handler as POST };
