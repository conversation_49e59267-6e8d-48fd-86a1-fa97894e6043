"use client";

import { signOut } from "next-auth/react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

export default function LogoutButton() {
  const [isLoading, setIsLoading] = useState(false);
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations("AdminDashboard");

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await signOut({
        callbackUrl: `/${locale}/admin/login`,
        redirect: true,
      });
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleLogout}
      disabled={isLoading}
      variant="destructive"
      size="sm"
      className="gap-2"
    >
      {isLoading ? (
        <>
          <Icon icon="lucide:loader-2" className="h-4 w-4 animate-spin" />
          {t("signingOut")}
        </>
      ) : (
        <>
          <Icon icon="lucide:log-out" className="h-4 w-4" />
          {t("logout")}
        </>
      )}
    </Button>
  );
}
