"use client";

import { signIn, signOut } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

type AuthButtonProps = {
  mode: "login" | "logout";
  // Login-specific props
  email?: string;
  password?: string;
  isLoading?: boolean;
  setIsLoading?: (val: boolean) => void;
  setErrorMsg?: (val: string) => void;
  label?: string;
  loadingLabel?: string;
};

export default function AuthButton({
  mode,
  email,
  password,
  isLoading: externalIsLoading,
  setIsLoading: externalSetIsLoading,
  setErrorMsg,
  label,
  loadingLabel,
}: AuthButtonProps) {
  const router = useRouter();
  const { locale } = useParams();
  const [internalIsLoading, setInternalIsLoading] = useState(false);
  const t = useTranslations(mode === "login" ? "AdminLogin" : "AdminDashboard");

  // Use external loading state for login, internal for logout
  const isLoading =
    mode === "login" ? externalIsLoading || false : internalIsLoading;
  const setIsLoading =
    mode === "login"
      ? externalSetIsLoading || (() => {})
      : setInternalIsLoading;

  async function handleLogin(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    setErrorMsg?.("");

    if (!email || !password) {
      setErrorMsg?.("Email and password required");
      setIsLoading(false);
      return;
    }

    try {
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
        callbackUrl: `/${locale}/admin`,
      });

      if (result?.error) {
        setErrorMsg?.(result.error);
      } else {
        router.push(`/${locale}/admin`);
      }
    } catch (error) {
      setErrorMsg?.("Unexpected error");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleLogout() {
    setIsLoading(true);
    try {
      await signOut({
        callbackUrl: `/${locale}/admin/login`,
        redirect: true,
      });
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoading(false);
    }
  }

  const handleClick = mode === "login" ? handleLogin : handleLogout;

  // Get appropriate labels and icons
  const getButtonContent = () => {
    if (mode === "login") {
      const buttonLabel = label || t("submit");
      const buttonLoadingLabel = loadingLabel || t("signingIn");

      return {
        icon: "lucide:log-in",
        label: buttonLabel,
        loadingLabel: buttonLoadingLabel,
        iconSize: "h-5 w-5",
      };
    } else {
      return {
        icon: "lucide:log-out",
        label: t("logout"),
        loadingLabel: t("signingOut"),
        iconSize: "h-4 w-4",
      };
    }
  };

  const {
    icon,
    label: buttonLabel,
    loadingLabel: buttonLoadingLabel,
    iconSize,
  } = getButtonContent();

  return (
    <Button
      type={mode === "login" ? "submit" : "button"}
      disabled={isLoading}
      className={mode === "login" ? "w-full gap-2" : "gap-2"}
      size={mode === "login" ? "lg" : "sm"}
      variant={mode === "logout" ? "destructive" : "default"}
      onClick={handleClick}
    >
      {isLoading ? (
        <>
          <Icon icon="lucide:loader-2" className={`${iconSize} animate-spin`} />
          {buttonLoadingLabel}
        </>
      ) : (
        <>
          <Icon icon={icon} className={iconSize} />
          {buttonLabel}
        </>
      )}
    </Button>
  );
}
