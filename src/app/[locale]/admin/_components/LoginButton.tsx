"use client";

import { signIn } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

type LoginButtonProps = {
  email: string;
  password: string;
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
  setErrorMsg: (val: string) => void;
  label: string;
  loadingLabel: string;
};

export default function LoginButton({
  email,
  password,
  isLoading,
  setIsLoading,
  setErrorMsg,
  label,
  loadingLabel,
}: LoginButtonProps) {
  const router = useRouter();
  const { locale } = useParams();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    setErrorMsg("");

    if (!email || !password) {
      setErrorMsg("Email and password required");
      setIsLoading(false);
      return;
    }

    try {
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
        callbackUrl: `/${locale}/admin`,
      });

      if (result?.error) {
        setErrorMsg(result.error);
      } else {
        router.push(`/${locale}/admin`);
      }
    } catch (error) {
      setErrorMsg("Unexpected error");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Button
      type="submit"
      disabled={isLoading}
      className="w-full gap-2"
      size="lg"
      onClick={handleSubmit}
    >
      {isLoading ? (
        <>
          <Icon icon="lucide:loader-2" className="h-5 w-5 animate-spin" />
          {loadingLabel}
        </>
      ) : (
        <>
          <Icon icon="lucide:log-in" className="h-5 w-5" />
          {label}
        </>
      )}
    </Button>
  );
}
