"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import AuthButton from "../_components/AuthButton";

export default function AdminLogin() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMsg, setErrorMsg] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations("AdminLogin");

  return (
    <div className="max-w-md mx-auto mt-20 p-8 bg-gray-900 rounded-lg border border-gray-800">
      <h1 className="text-2xl font-bold mb-6 text-white text-center">
        {t("title")}
      </h1>

      {errorMsg && (
        <div className="mb-4 text-red-400 text-center">{errorMsg}</div>
      )}

      <form className="space-y-4">
        <div>
          <label className="block text-white mb-2">{t("email")}</label>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
            className="bg-gray-800 border-gray-600 text-white focus:border-blue-500"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-white mb-2">{t("password")}</label>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            className="bg-gray-800 border-gray-600 text-white focus:border-blue-500"
            placeholder="••••••••"
          />
        </div>

        <AuthButton
          mode="login"
          email={email}
          password={password}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          setErrorMsg={setErrorMsg}
          label={t("submit")}
          loadingLabel={t("signingIn")}
        />
      </form>
    </div>
  );
}
