"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";

export function LanguageSwitcher({
  currentLocale,
  locales,
}: {
  currentLocale: string;
  locales: readonly string[];
}) {
  const pathname = usePathname();
  const strippedPath = pathname.replace(/^\/(en|jp)/, "");

  return (
    <div className="flex justify-end space-x-2 p-4">
      {locales.map((loc) => (
        <Link
          key={loc}
          href={`/${loc}${strippedPath}`}
          className={`px-4 py-2 rounded ${
            loc === currentLocale
              ? "bg-white text-black"
              : "bg-black border-1 border-gray-100 text-white"
          }`}
        >
          {loc.toUpperCase()}
        </Link>
      ))}
    </div>
  );
}
