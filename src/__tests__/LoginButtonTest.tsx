import LoginButton from "@/app/[locale]/admin/_components/LoginButton";
import { render, fireEvent, screen, waitFor } from "@testing-library/react";
import { signIn } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";

jest.mock("next-auth/react", () => ({
  signIn: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(() => ({ locale: "en" })),
}));

describe("LoginButton", () => {
  const push = jest.fn();

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({ push });
    jest.clearAllMocks();
  });

  it("shows error if email or password is missing", async () => {
    const setErrorMsg = jest.fn();
    const setIsLoading = jest.fn();

    render(
      <LoginButton
        email=""
        password=""
        isLoading={false}
        setIsLoading={setIsLoading}
        setErrorMsg={setErrorMsg}
        label="Sign In"
        loadingLabel="Signing In..."
      />
    );

    fireEvent.click(screen.getByText("Sign In"));

    await waitFor(() => {
      expect(setErrorMsg).toHaveBeenCalledWith("Email and password required");
    });
  });

  it("calls signIn and redirects on success", async () => {
    (signIn as jest.Mock).mockResolvedValue({ error: null });
    const setIsLoading = jest.fn();
    const setErrorMsg = jest.fn();

    render(
      <LoginButton
        email="<EMAIL>"
        password="password123"
        isLoading={false}
        setIsLoading={setIsLoading}
        setErrorMsg={setErrorMsg}
        label="Sign In"
        loadingLabel="Signing In..."
      />
    );

    fireEvent.click(screen.getByText("Sign In"));

    await waitFor(() => {
      expect(signIn).toHaveBeenCalledWith("credentials", {
        redirect: false,
        email: "<EMAIL>",
        password: "password123",
        callbackUrl: "/en/admin",
      });
      expect(push).toHaveBeenCalledWith("/en/admin");
    });
  });

  it("shows error on failed login", async () => {
    (signIn as jest.Mock).mockResolvedValue({ error: "Invalid credentials" });
    const setIsLoading = jest.fn();
    const setErrorMsg = jest.fn();

    render(
      <LoginButton
        email="<EMAIL>"
        password="wrongpass"
        isLoading={false}
        setIsLoading={setIsLoading}
        setErrorMsg={setErrorMsg}
        label="Sign In"
        loadingLabel="Signing In..."
      />
    );

    fireEvent.click(screen.getByText("Sign In"));

    await waitFor(() => {
      expect(setErrorMsg).toHaveBeenCalledWith("Invalid credentials");
    });
  });
});
